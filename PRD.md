# 家庭相册管理网站 - 产品需求文档 (PRD)

## 1. 产品概述和目标用户

### 1.1 产品概述
家庭相册管理网站是一个基于Python Flask框架开发的Web应用程序，旨在为家庭用户提供一个安全、便捷的数字相册管理平台。用户可以上传、组织、分享和管理家庭照片，创建美好的数字回忆空间。

### 1.2 产品目标
- 提供简单易用的家庭照片管理解决方案
- 实现照片的安全存储和隐私保护
- 支持多用户家庭成员协作管理
- 采用完全免费的技术栈，降低使用成本
- 作为Python Flask学习实践项目

### 1.3 目标用户
- **主要用户**：家庭主要成员（父母）
- **次要用户**：家庭其他成员（子女、老人）
- **用户特征**：
  - 年龄范围：25-60岁
  - 技术水平：初级到中级
  - 使用场景：家庭照片整理、回忆分享、节日纪念

## 2. 核心功能需求列表

### 2.1 用户管理功能
- **用户注册/登录**
  - 邮箱注册验证
  - 密码安全策略
  - 记住登录状态
  - 密码重置功能

- **用户权限管理**
  - 管理员权限（家庭主账户）
  - 普通用户权限（家庭成员）
  - 访客权限（仅查看）

### 2.2 相册管理功能
- **相册创建与组织**
  - 创建主题相册（如：生日、旅行、节日等）
  - 相册封面设置
  - 相册描述和标签
  - 相册排序和分类

- **照片上传与管理**
  - 批量照片上传
  - 支持常见图片格式（JPG、PNG、GIF、WEBP）
  - 图片自动压缩和缩略图生成
  - 照片元数据提取（拍摄时间、地点等）

### 2.3 照片浏览与展示
- **照片浏览**
  - 网格视图和列表视图
  - 照片放大查看
  - 照片轮播展示
  - 按时间线浏览

- **搜索与筛选**
  - 按日期范围搜索
  - 按相册分类筛选
  - 按标签搜索
  - 智能搜索建议

### 2.4 分享与协作
- **分享功能**
  - 生成分享链接
  - 设置分享权限和有效期
  - 社交媒体分享集成
  - 下载原图功能

- **评论与互动**
  - 照片评论功能
  - 点赞收藏功能
  - 家庭成员互动

## 3. 用户界面设计要求

### 3.1 设计原则
- **简洁性**：界面简洁明了，避免复杂操作
- **响应式**：支持桌面端、平板和移动端
- **易用性**：符合用户习惯的交互设计
- **美观性**：温馨的家庭风格设计

### 3.2 主要页面设计
- **首页**：展示最新照片和热门相册
- **相册列表页**：网格布局展示所有相册
- **相册详情页**：照片网格展示，支持筛选排序
- **照片详情页**：大图展示，包含元数据和评论
- **用户中心**：个人信息管理和设置
- **上传页面**：拖拽上传界面，进度显示

### 3.3 UI框架选择
- **Bootstrap 5**：响应式布局和组件
- **Font Awesome**：图标库
- **自定义CSS**：家庭温馨主题色彩

## 4. 技术架构建议

### 4.1 后端架构
```
Flask应用架构：
├── app/
│   ├── __init__.py          # Flask应用初始化
│   ├── models/              # 数据模型
│   │   ├── user.py
│   │   ├── album.py
│   │   └── photo.py
│   ├── views/               # 视图控制器
│   │   ├── auth.py          # 认证相关
│   │   ├── album.py         # 相册管理
│   │   └── photo.py         # 照片管理
│   ├── templates/           # HTML模板
│   ├── static/              # 静态资源
│   └── utils/               # 工具函数
├── config.py                # 配置文件
├── requirements.txt         # 依赖包
└── run.py                   # 应用启动文件
```

### 4.2 核心技术栈
- **Web框架**：Flask 2.3+
- **数据库ORM**：SQLAlchemy
- **用户认证**：Flask-Login
- **表单处理**：Flask-WTF
- **图片处理**：Pillow
- **文件上传**：Flask-Uploads
- **数据库迁移**：Flask-Migrate

### 4.3 前端技术
- **模板引擎**：Jinja2
- **CSS框架**：Bootstrap 5
- **JavaScript**：原生JS + jQuery
- **图片展示**：Lightbox或Fancybox

## 5. 数据库设计概要

### 5.1 数据库选择
- **开发环境**：MySQL（轻量级，无需安装）
- **生产环境**：MySQL（免费，稳定可靠）
- **云数据库**：MySQL（免费额度）

### 5.2 核心数据表设计

#### 用户表 (users)
```sql
- id (主键)
- username (用户名，唯一)
- email (邮箱，唯一)
- password_hash (密码哈希)
- role (用户角色：admin/user/guest)
- created_at (创建时间)
- last_login (最后登录时间)
```

#### 相册表 (albums)
```sql
- id (主键)
- name (相册名称)
- description (相册描述)
- cover_photo_id (封面照片ID)
- user_id (创建者ID，外键)
- is_public (是否公开)
- created_at (创建时间)
- updated_at (更新时间)
```

#### 照片表 (photos)
```sql
- id (主键)
- filename (文件名)
- original_filename (原始文件名)
- file_path (文件路径)
- thumbnail_path (缩略图路径)
- file_size (文件大小)
- width (图片宽度)
- height (图片高度)
- album_id (所属相册ID，外键)
- user_id (上传者ID，外键)
- taken_at (拍摄时间)
- uploaded_at (上传时间)
```

#### 评论表 (comments)
```sql
- id (主键)
- content (评论内容)
- photo_id (照片ID，外键)
- user_id (评论者ID，外键)
- created_at (创建时间)
```

## 6. 图片存储方案分析

### 6.1 Cloudflare R2存储方案
**优势：**
- 免费额度：10GB存储空间
- 全球CDN加速
- S3兼容API
- 无出站流量费用

**技术实现：**
- 使用boto3库连接R2
- 实现图片上传、下载、删除功能
- 自动生成缩略图并存储

**配置示例：**
```python
import boto3
from botocore.config import Config

r2_client = boto3.client(
    's3',
    endpoint_url='https://your-account-id.r2.cloudflarestorage.com',
    aws_access_key_id='your-access-key',
    aws_secret_access_key='your-secret-key',
    config=Config(signature_version='s3v4')
)
```

### 6.2 备选方案对比

| 存储方案 | 免费额度 | 优势 | 劣势 |
|---------|---------|------|------|
| Cloudflare R2 | 10GB | 无流量费用，CDN加速 | 相对较新的服务 |
| AWS S3 | 5GB/12个月 | 成熟稳定，生态丰富 | 流量费用较高 |
| 本地存储 | 无限制 | 完全免费，完全控制 | 无CDN，备份困难 |
| GitHub LFS | 1GB | 版本控制集成 | 不适合大量图片 |

**推荐方案：**
1. **开发阶段**：本地存储
2. **生产部署**：Cloudflare R2
3. **备份策略**：定期备份到本地或其他云存储

## 7. 项目开发阶段规划

### 阶段一：基础框架搭建（1-2周）
- [ ] Flask项目初始化
- [ ] 数据库模型设计和创建
- [ ] 用户认证系统
- [ ] 基础页面模板

### 阶段二：核心功能开发（2-3周）
- [ ] 相册创建和管理
- [ ] 照片上传功能
- [ ] 图片处理和缩略图生成
- [ ] 基础浏览功能

### 阶段三：高级功能实现（2-3周）
- [ ] 搜索和筛选功能
- [ ] 分享功能
- [ ] 评论和互动功能
- [ ] 响应式界面优化

### 阶段四：云存储集成（1-2周）
- [ ] Cloudflare R2集成
- [ ] 图片迁移工具
- [ ] CDN配置优化
- [ ] 性能测试

### 阶段五：测试和部署（1-2周）
- [ ] 功能测试
- [ ] 安全性测试
- [ ] 性能优化
- [ ] 生产环境部署

## 8. 安全性和隐私保护考虑

### 8.1 数据安全
- **密码安全**：使用bcrypt进行密码哈希
- **会话管理**：安全的session配置
- **CSRF保护**：使用Flask-WTF的CSRF令牌
- **文件上传安全**：文件类型验证和大小限制

### 8.2 隐私保护
- **访问控制**：基于用户角色的权限管理
- **数据加密**：敏感数据传输加密
- **隐私设置**：用户可控制照片可见性
- **数据备份**：定期备份用户数据

### 8.3 合规性考虑
- **GDPR合规**：用户数据删除权
- **数据最小化**：只收集必要的用户信息
- **透明度**：清晰的隐私政策
- **用户控制**：用户可导出和删除自己的数据

---

## 项目成功指标

1. **功能完整性**：实现所有核心功能
2. **用户体验**：界面友好，操作流畅
3. **性能表现**：页面加载速度 < 3秒
4. **安全性**：无重大安全漏洞
5. **学习目标**：掌握Flask全栈开发技能

## 后续扩展计划

- 移动端APP开发
- AI智能标签识别
- 人脸识别和分类
- 视频文件支持
- 多语言国际化支持

---

*本PRD文档将作为项目开发的指导文档，在开发过程中可根据实际情况进行调整和完善。*
